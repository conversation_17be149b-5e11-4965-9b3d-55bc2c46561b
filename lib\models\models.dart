import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

part 'models.g.dart'; // <- generated code

class TimestampDateTimeConverter implements JsonConverter<DateTime, dynamic> {
  const TimestampDateTimeConverter();

  @override
  DateTime fromJson(dynamic json) {
    if (json == null) {
      throw Exception('Cannot convert null to DateTime');
    }
    if (json is Timestamp) return json.toDate();
    if (json is String) {
      final parsed = DateTime.tryParse(json);
      if (parsed == null) {
        throw Exception('Cannot parse string "$json" to DateTime');
      }
      return parsed;
    }
    throw Exception('Cannot convert $json to DateTime');
  }

  @override
  dynamic toJson(DateTime object) {
    return Timestamp.fromDate(object);
  }
}

class NullableTimestampDateTimeConverter
    implements JsonConverter<DateTime?, dynamic> {
  const NullableTimestampDateTimeConverter();

  @override
  DateTime? fromJson(dynamic json) {
    if (json == null) {
      return null;
    }
    if (json is Timestamp) return json.toDate();
    if (json is String) {
      final parsed = DateTime.tryParse(json);
      if (parsed == null) {
        throw Exception('Cannot parse string "$json" to DateTime');
      }
      return parsed;
    }
    throw Exception('Cannot convert $json to DateTime');
  }

  @override
  dynamic toJson(DateTime? object) {
    if (object == null) return null;
    return Timestamp.fromDate(object);
  }
}

@JsonSerializable()
class User {
  final String id;
  final String name;
  final String email;
  final bool isOnboarded;
  final bool isAdmin;
  final String? description;
  final List<String> preferredPersonaIds;

  @TimestampDateTimeConverter()
  final DateTime createdAt;

  User({
    required this.id,
    required this.name,
    required this.email,
    this.isOnboarded = false,
    this.isAdmin = false,
    this.description,
    this.preferredPersonaIds = const [],
    required this.createdAt,
  });

  // From JSON factory
  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$UserToJson(this);

  // Copy with method for updating user data
  User copyWith({
    String? id,
    String? name,
    String? email,
    bool? isOnboarded,
    bool? isAdmin,
    String? description,
    List<String>? preferredPersonaIds,
    DateTime? createdAt,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      isOnboarded: isOnboarded ?? this.isOnboarded,
      isAdmin: isAdmin ?? this.isAdmin,
      description: description ?? this.description,
      preferredPersonaIds: preferredPersonaIds ?? this.preferredPersonaIds,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

@JsonSerializable()
class ChatParticipant {
  final String personaId;
  final String name;
  final String avatarUrl;

  ChatParticipant({
    required this.personaId,
    required this.name,
    required this.avatarUrl,
  });

  // From JSON factory
  factory ChatParticipant.fromJson(Map<String, dynamic> json) =>
      _$ChatParticipantFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$ChatParticipantToJson(this);
}

@JsonSerializable(explicitToJson: true)
class Chat {
  final String? id; // Firestore document ID
  final String title;
  final String ownerId; // User uid

  @TimestampDateTimeConverter()
  final DateTime startedDate;

  @TimestampDateTimeConverter()
  final DateTime lastUpdatedDate;

  final bool isCompleted;
  final List<ChatParticipant> participants;
  final bool? archived;
  final Map<String, dynamic>? metadata;

  Chat({
    this.id,
    required this.title,
    required this.ownerId,
    required this.startedDate,
    required this.lastUpdatedDate,
    required this.isCompleted,
    required this.participants,
    this.archived,
    this.metadata,
  });

  // From JSON factory
  factory Chat.fromJson(Map<String, dynamic> json) => _$ChatFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$ChatToJson(this);
}

@JsonSerializable(explicitToJson: true)
class Message {
  final String? id; // Firestore document ID
  final String chatId;
  final String chatOwnerId; // User uid from Chat entity for efficient rules
  final String? userId; // Message author (Firebase userId)
  final String? systemPersonaId;

  @TimestampDateTimeConverter()
  final DateTime postedDate;

  final bool isDeleted;
  final String type;
  final String? textContent;
  final String? imageUrl;
  final Map<String, int> reactionCounts;
  final bool? edited;

  @NullableTimestampDateTimeConverter()
  final DateTime? editDate;

  final String? replyToMessageId;
  final Map<String, dynamic>? metadata;

  Message({
    this.id,
    required this.chatId,
    required this.chatOwnerId,
    required this.userId,
    this.systemPersonaId,
    required this.postedDate,
    required this.isDeleted,
    required this.type,
    this.textContent,
    this.imageUrl,
    required this.reactionCounts,
    this.edited,
    this.editDate,
    this.replyToMessageId,
    this.metadata,
  });

  // From JSON factory
  factory Message.fromJson(Map<String, dynamic> json) =>
      _$MessageFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$MessageToJson(this);
}

@JsonSerializable()
class SystemPersona {
  final String? id; // Firestore document ID
  final String name;
  final String? avatarUrl;
  final String? description;
  final bool? isActive;
  final Map<String, dynamic>? metadata;

  SystemPersona({
    this.id,
    required this.name,
    this.avatarUrl,
    this.description,
    this.isActive,
    this.metadata,
  });

  // From JSON factory
  factory SystemPersona.fromJson(Map<String, dynamic> json) =>
      _$SystemPersonaFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$SystemPersonaToJson(this);
}

@JsonSerializable()
class RelationInfo {
  final String name;
  final int age;
  final String relation; // e.g. 'son', 'spouse', 'dog', etc.
  final List<String>? otherInfo; // Additional notes about this relation

  RelationInfo({
    required this.name,
    required this.age,
    required this.relation,
    this.otherInfo,
  });

  // From JSON factory
  factory RelationInfo.fromJson(Map<String, dynamic> json) =>
      _$RelationInfoFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$RelationInfoToJson(this);

  // Copy with method
  RelationInfo copyWith({
    String? name,
    int? age,
    String? relation,
    List<String>? otherInfo,
  }) {
    return RelationInfo(
      name: name ?? this.name,
      age: age ?? this.age,
      relation: relation ?? this.relation,
      otherInfo: otherInfo ?? this.otherInfo,
    );
  }
}

@JsonSerializable()
class Location {
  final String? town;
  final String country;

  Location({this.town, required this.country});

  // From JSON factory
  factory Location.fromJson(Map<String, dynamic> json) =>
      _$LocationFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$LocationToJson(this);

  // Copy with method
  Location copyWith({String? town, String? country}) {
    return Location(town: town ?? this.town, country: country ?? this.country);
  }
}

@JsonSerializable()
class Fact {
  final String key;
  final dynamic value; // Can be string, number, or boolean

  Fact({required this.key, required this.value});

  // From JSON factory
  factory Fact.fromJson(Map<String, dynamic> json) => _$FactFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$FactToJson(this);

  // Copy with method
  Fact copyWith({String? key, dynamic value}) {
    return Fact(key: key ?? this.key, value: value ?? this.value);
  }
}

@JsonSerializable()
class Goal {
  final String id;
  final String description;
  final String status; // 'not_started', 'in_progress', 'achieved', 'abandoned'

  @TimestampDateTimeConverter()
  final DateTime createdAt;

  @NullableTimestampDateTimeConverter()
  final DateTime? updatedAt;

  Goal({
    required this.id,
    required this.description,
    required this.status,
    required this.createdAt,
    this.updatedAt,
  });

  // From JSON factory
  factory Goal.fromJson(Map<String, dynamic> json) => _$GoalFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$GoalToJson(this);

  // Copy with method
  Goal copyWith({
    String? id,
    String? description,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Goal(
      id: id ?? this.id,
      description: description ?? this.description,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

@JsonSerializable()
class InteractionSource {
  final String sessionId;

  @TimestampDateTimeConverter()
  final DateTime timestamp;

  InteractionSource({required this.sessionId, required this.timestamp});

  // From JSON factory
  factory InteractionSource.fromJson(Map<String, dynamic> json) =>
      _$InteractionSourceFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$InteractionSourceToJson(this);

  // Copy with method
  InteractionSource copyWith({String? sessionId, DateTime? timestamp}) {
    return InteractionSource(
      sessionId: sessionId ?? this.sessionId,
      timestamp: timestamp ?? this.timestamp,
    );
  }
}

@JsonSerializable(explicitToJson: true)
class UserProfile {
  final String userId;
  final String? name;
  final int? age;
  final String?
  gender; // 'male', 'female', 'non-binary', 'other', 'unspecified'
  final String?
  familyStatus; // 'single', 'married', 'partnered', 'divorced', 'widowed', 'unspecified'
  final List<RelationInfo>? family;
  final Location? location;
  final List<Fact>? facts;
  final List<String>? likes;
  final List<String>? dislikes;
  final Map<String, dynamic>? preferences;
  final List<Goal>? goals;
  final List<String>? personalityTraits;
  final InteractionHistory interactionHistory;

  UserProfile({
    required this.userId,
    this.name,
    this.age,
    this.gender,
    this.familyStatus,
    this.family,
    this.location,
    this.facts,
    this.likes,
    this.dislikes,
    this.preferences,
    this.goals,
    this.personalityTraits,
    required this.interactionHistory,
  });

  // From JSON factory
  factory UserProfile.fromJson(Map<String, dynamic> json) =>
      _$UserProfileFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$UserProfileToJson(this);

  // Copy with method
  UserProfile copyWith({
    String? userId,
    String? name,
    int? age,
    String? gender,
    String? familyStatus,
    List<RelationInfo>? family,
    Location? location,
    List<Fact>? facts,
    List<String>? likes,
    List<String>? dislikes,
    Map<String, dynamic>? preferences,
    List<Goal>? goals,
    List<String>? personalityTraits,
    InteractionHistory? interactionHistory,
  }) {
    return UserProfile(
      userId: userId ?? this.userId,
      name: name ?? this.name,
      age: age ?? this.age,
      gender: gender ?? this.gender,
      familyStatus: familyStatus ?? this.familyStatus,
      family: family ?? this.family,
      location: location ?? this.location,
      facts: facts ?? this.facts,
      likes: likes ?? this.likes,
      dislikes: dislikes ?? this.dislikes,
      preferences: preferences ?? this.preferences,
      goals: goals ?? this.goals,
      personalityTraits: personalityTraits ?? this.personalityTraits,
      interactionHistory: interactionHistory ?? this.interactionHistory,
    );
  }
}

@JsonSerializable(explicitToJson: true)
class InteractionHistory {
  @TimestampDateTimeConverter()
  final DateTime lastUpdated;

  final List<InteractionSource>? sources;

  InteractionHistory({required this.lastUpdated, this.sources});

  // From JSON factory
  factory InteractionHistory.fromJson(Map<String, dynamic> json) =>
      _$InteractionHistoryFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$InteractionHistoryToJson(this);

  // Copy with method
  InteractionHistory copyWith({
    DateTime? lastUpdated,
    List<InteractionSource>? sources,
  }) {
    return InteractionHistory(
      lastUpdated: lastUpdated ?? this.lastUpdated,
      sources: sources ?? this.sources,
    );
  }
}
