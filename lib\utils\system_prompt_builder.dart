import '../models/models.dart';

/// Utility class for building comprehensive system prompts that incorporate
/// UserProfile information and context for AI coaching conversations.
class SystemPromptBuilder {
  /// Builds a comprehensive system prompt that includes AI role definition,
  /// user context, and conversation objectives.
  static String buildSystemPrompt({
    UserProfile? userProfile,
    DateTime? currentTime,
    String? chatObjective,
    List<String>? additionalInstructions,
  }) {
    final buffer = StringBuffer();
    final now = currentTime ?? DateTime.now();
    
    // AI Role Definition and Core Instructions
    buffer.writeln(_buildAIRoleSection());
    buffer.writeln();
    
    // Current Context
    buffer.writeln(_buildContextSection(now));
    buffer.writeln();
    
    // User Profile Information
    if (userProfile != null) {
      buffer.writeln(_buildUserProfileSection(userProfile));
      buffer.writeln();
    }
    
    // Chat Objectives
    if (chatObjective != null && chatObjective.isNotEmpty) {
      buffer.writeln(_buildObjectiveSection(chatObjective));
      buffer.writeln();
    }
    
    // Additional Instructions
    if (additionalInstructions != null && additionalInstructions.isNotEmpty) {
      buffer.writeln(_buildAdditionalInstructionsSection(additionalInstructions));
      buffer.writeln();
    }
    
    // Response Guidelines
    buffer.writeln(_buildResponseGuidelinesSection());
    
    return buffer.toString().trim();
  }

  static String _buildAIRoleSection() {
    return '''# AI Coach Role & Identity

You are an AI coaching assistant designed to provide personalized guidance, support, and motivation. Your core characteristics:

- **Empathetic & Understanding**: Listen actively and respond with genuine care
- **Adaptive Communication**: Adjust your style based on user preferences and personality
- **Goal-Oriented**: Help users identify, pursue, and achieve their objectives
- **Evidence-Based**: Provide practical, actionable advice grounded in coaching principles
- **Respectful Boundaries**: Maintain professional coaching relationships
- **Growth-Focused**: Encourage continuous learning and development

You are NOT a therapist or medical professional. For serious mental health or medical concerns, encourage users to seek appropriate professional help.''';
  }

  static String _buildContextSection(DateTime currentTime) {
    final timeZone = currentTime.timeZoneName;
    final formattedTime = currentTime.toIso8601String();
    final dayOfWeek = _getDayOfWeek(currentTime.weekday);
    
    return '''# Current Context

- **Current Time**: $formattedTime ($timeZone)
- **Day**: $dayOfWeek
- **Session Type**: AI Coaching Conversation''';
  }

  static String _buildUserProfileSection(UserProfile userProfile) {
    final buffer = StringBuffer();
    buffer.writeln('# User Profile');
    buffer.writeln();
    
    // Basic Information
    if (userProfile.name != null || userProfile.age != null || userProfile.gender != null) {
      buffer.writeln('## Basic Information');
      if (userProfile.name != null) {
        buffer.writeln('- **Name**: ${userProfile.name}');
      }
      if (userProfile.age != null) {
        buffer.writeln('- **Age**: ${userProfile.age}');
      }
      if (userProfile.gender != null) {
        buffer.writeln('- **Gender**: ${userProfile.gender}');
      }
      if (userProfile.familyStatus != null) {
        buffer.writeln('- **Family Status**: ${userProfile.familyStatus}');
      }
      if (userProfile.location != null) {
        final location = userProfile.location!;
        final locationStr = location.town != null 
            ? '${location.town}, ${location.country}'
            : location.country;
        buffer.writeln('- **Location**: $locationStr');
      }
      buffer.writeln();
    }
    
    // Family & Relationships
    if (userProfile.family != null && userProfile.family!.isNotEmpty) {
      buffer.writeln('## Family & Relationships');
      for (final relation in userProfile.family!) {
        buffer.write('- **${relation.name}** (${relation.relation}, age ${relation.age})');
        if (relation.otherInfo != null && relation.otherInfo!.isNotEmpty) {
          buffer.write(' - ${relation.otherInfo!.join(', ')}');
        }
        buffer.writeln();
      }
      buffer.writeln();
    }
    
    // Goals
    if (userProfile.goals != null && userProfile.goals!.isNotEmpty) {
      buffer.writeln('## Current Goals');
      for (final goal in userProfile.goals!) {
        buffer.writeln('- **${goal.description}** (Status: ${goal.status})');
      }
      buffer.writeln();
    }
    
    // Preferences & Interests
    if (userProfile.likes != null && userProfile.likes!.isNotEmpty) {
      buffer.writeln('## Interests & Likes');
      buffer.writeln('- ${userProfile.likes!.join(', ')}');
      buffer.writeln();
    }
    
    if (userProfile.dislikes != null && userProfile.dislikes!.isNotEmpty) {
      buffer.writeln('## Dislikes');
      buffer.writeln('- ${userProfile.dislikes!.join(', ')}');
      buffer.writeln();
    }
    
    // Personality Traits
    if (userProfile.personalityTraits != null && userProfile.personalityTraits!.isNotEmpty) {
      buffer.writeln('## Personality Traits');
      buffer.writeln('- ${userProfile.personalityTraits!.join(', ')}');
      buffer.writeln();
    }
    
    // Key Facts
    if (userProfile.facts != null && userProfile.facts!.isNotEmpty) {
      buffer.writeln('## Important Facts');
      for (final fact in userProfile.facts!) {
        buffer.writeln('- **${fact.key}**: ${fact.value}');
      }
      buffer.writeln();
    }
    
    // Preferences
    if (userProfile.preferences != null && userProfile.preferences!.isNotEmpty) {
      buffer.writeln('## Communication Preferences');
      userProfile.preferences!.forEach((key, value) {
        buffer.writeln('- **$key**: $value');
      });
      buffer.writeln();
    }
    
    return buffer.toString().trimRight();
  }

  static String _buildObjectiveSection(String objective) {
    return '''# Session Objective

$objective''';
  }

  static String _buildAdditionalInstructionsSection(List<String> instructions) {
    final buffer = StringBuffer();
    buffer.writeln('# Additional Instructions');
    buffer.writeln();
    for (final instruction in instructions) {
      buffer.writeln('- $instruction');
    }
    return buffer.toString().trimRight();
  }

  static String _buildResponseGuidelinesSection() {
    return '''# Response Guidelines

- **Personalization**: Use the user's name and reference their profile information naturally
- **Tone**: Match the user's communication style and preferences
- **Length**: Provide thoughtful, comprehensive responses without being overwhelming
- **Actionability**: Include specific, practical next steps when appropriate
- **Encouragement**: Acknowledge progress and celebrate achievements
- **Curiosity**: Ask thoughtful follow-up questions to deepen understanding
- **Respect**: Honor the user's values, goals, and boundaries

Remember: This profile information should inform your responses naturally, but don't explicitly reference having access to this data unless directly relevant to the conversation.''';
  }

  static String _getDayOfWeek(int weekday) {
    const days = [
      'Monday', 'Tuesday', 'Wednesday', 'Thursday', 
      'Friday', 'Saturday', 'Sunday'
    ];
    return days[weekday - 1];
  }
}
