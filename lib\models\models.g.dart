// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

User _$UserFromJson(Map<String, dynamic> json) => User(
  id: json['id'] as String,
  name: json['name'] as String,
  email: json['email'] as String,
  isOnboarded: json['isOnboarded'] as bool? ?? false,
  isAdmin: json['isAdmin'] as bool? ?? false,
  description: json['description'] as String?,
  preferredPersonaIds:
      (json['preferredPersonaIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  createdAt: const TimestampDateTimeConverter().fromJson(json['createdAt']),
);

Map<String, dynamic> _$UserToJson(User instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'email': instance.email,
  'isOnboarded': instance.isOnboarded,
  'isAdmin': instance.isAdmin,
  'description': instance.description,
  'preferredPersonaIds': instance.preferredPersonaIds,
  'createdAt': const TimestampDateTimeConverter().toJson(instance.createdAt),
};

ChatParticipant _$ChatParticipantFromJson(Map<String, dynamic> json) =>
    ChatParticipant(
      personaId: json['personaId'] as String,
      name: json['name'] as String,
      avatarUrl: json['avatarUrl'] as String,
    );

Map<String, dynamic> _$ChatParticipantToJson(ChatParticipant instance) =>
    <String, dynamic>{
      'personaId': instance.personaId,
      'name': instance.name,
      'avatarUrl': instance.avatarUrl,
    };

Chat _$ChatFromJson(Map<String, dynamic> json) => Chat(
  id: json['id'] as String?,
  title: json['title'] as String,
  ownerId: json['ownerId'] as String,
  startedDate: const TimestampDateTimeConverter().fromJson(json['startedDate']),
  lastUpdatedDate: const TimestampDateTimeConverter().fromJson(
    json['lastUpdatedDate'],
  ),
  isCompleted: json['isCompleted'] as bool,
  participants: (json['participants'] as List<dynamic>)
      .map((e) => ChatParticipant.fromJson(e as Map<String, dynamic>))
      .toList(),
  archived: json['archived'] as bool?,
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$ChatToJson(Chat instance) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'ownerId': instance.ownerId,
  'startedDate': const TimestampDateTimeConverter().toJson(
    instance.startedDate,
  ),
  'lastUpdatedDate': const TimestampDateTimeConverter().toJson(
    instance.lastUpdatedDate,
  ),
  'isCompleted': instance.isCompleted,
  'participants': instance.participants.map((e) => e.toJson()).toList(),
  'archived': instance.archived,
  'metadata': instance.metadata,
};

Message _$MessageFromJson(Map<String, dynamic> json) => Message(
  id: json['id'] as String?,
  chatId: json['chatId'] as String,
  chatOwnerId: json['chatOwnerId'] as String,
  userId: json['userId'] as String?,
  systemPersonaId: json['systemPersonaId'] as String?,
  postedDate: const TimestampDateTimeConverter().fromJson(json['postedDate']),
  isDeleted: json['isDeleted'] as bool,
  type: json['type'] as String,
  textContent: json['textContent'] as String?,
  imageUrl: json['imageUrl'] as String?,
  reactionCounts: Map<String, int>.from(json['reactionCounts'] as Map),
  edited: json['edited'] as bool?,
  editDate: const NullableTimestampDateTimeConverter().fromJson(
    json['editDate'],
  ),
  replyToMessageId: json['replyToMessageId'] as String?,
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$MessageToJson(Message instance) => <String, dynamic>{
  'id': instance.id,
  'chatId': instance.chatId,
  'chatOwnerId': instance.chatOwnerId,
  'userId': instance.userId,
  'systemPersonaId': instance.systemPersonaId,
  'postedDate': const TimestampDateTimeConverter().toJson(instance.postedDate),
  'isDeleted': instance.isDeleted,
  'type': instance.type,
  'textContent': instance.textContent,
  'imageUrl': instance.imageUrl,
  'reactionCounts': instance.reactionCounts,
  'edited': instance.edited,
  'editDate': const NullableTimestampDateTimeConverter().toJson(
    instance.editDate,
  ),
  'replyToMessageId': instance.replyToMessageId,
  'metadata': instance.metadata,
};

SystemPersona _$SystemPersonaFromJson(Map<String, dynamic> json) =>
    SystemPersona(
      id: json['id'] as String?,
      name: json['name'] as String,
      avatarUrl: json['avatarUrl'] as String?,
      description: json['description'] as String?,
      isActive: json['isActive'] as bool?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$SystemPersonaToJson(SystemPersona instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'avatarUrl': instance.avatarUrl,
      'description': instance.description,
      'isActive': instance.isActive,
      'metadata': instance.metadata,
    };

RelationInfo _$RelationInfoFromJson(Map<String, dynamic> json) => RelationInfo(
  name: json['name'] as String,
  age: (json['age'] as num).toInt(),
  relation: json['relation'] as String,
  otherInfo: (json['otherInfo'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
);

Map<String, dynamic> _$RelationInfoToJson(RelationInfo instance) =>
    <String, dynamic>{
      'name': instance.name,
      'age': instance.age,
      'relation': instance.relation,
      'otherInfo': instance.otherInfo,
    };

Location _$LocationFromJson(Map<String, dynamic> json) =>
    Location(town: json['town'] as String?, country: json['country'] as String);

Map<String, dynamic> _$LocationToJson(Location instance) => <String, dynamic>{
  'town': instance.town,
  'country': instance.country,
};

Fact _$FactFromJson(Map<String, dynamic> json) =>
    Fact(key: json['key'] as String, value: json['value']);

Map<String, dynamic> _$FactToJson(Fact instance) => <String, dynamic>{
  'key': instance.key,
  'value': instance.value,
};

Goal _$GoalFromJson(Map<String, dynamic> json) => Goal(
  id: json['id'] as String,
  description: json['description'] as String,
  status: json['status'] as String,
  createdAt: const TimestampDateTimeConverter().fromJson(json['createdAt']),
  updatedAt: const NullableTimestampDateTimeConverter().fromJson(
    json['updatedAt'],
  ),
);

Map<String, dynamic> _$GoalToJson(Goal instance) => <String, dynamic>{
  'id': instance.id,
  'description': instance.description,
  'status': instance.status,
  'createdAt': const TimestampDateTimeConverter().toJson(instance.createdAt),
  'updatedAt': const NullableTimestampDateTimeConverter().toJson(
    instance.updatedAt,
  ),
};

InteractionSource _$InteractionSourceFromJson(Map<String, dynamic> json) =>
    InteractionSource(
      sessionId: json['sessionId'] as String,
      timestamp: const TimestampDateTimeConverter().fromJson(json['timestamp']),
    );

Map<String, dynamic> _$InteractionSourceToJson(
  InteractionSource instance,
) => <String, dynamic>{
  'sessionId': instance.sessionId,
  'timestamp': const TimestampDateTimeConverter().toJson(instance.timestamp),
};

UserProfile _$UserProfileFromJson(Map<String, dynamic> json) => UserProfile(
  userId: json['userId'] as String,
  name: json['name'] as String?,
  age: (json['age'] as num?)?.toInt(),
  gender: json['gender'] as String?,
  familyStatus: json['familyStatus'] as String?,
  family: (json['family'] as List<dynamic>?)
      ?.map((e) => RelationInfo.fromJson(e as Map<String, dynamic>))
      .toList(),
  location: json['location'] == null
      ? null
      : Location.fromJson(json['location'] as Map<String, dynamic>),
  facts: (json['facts'] as List<dynamic>?)
      ?.map((e) => Fact.fromJson(e as Map<String, dynamic>))
      .toList(),
  likes: (json['likes'] as List<dynamic>?)?.map((e) => e as String).toList(),
  dislikes: (json['dislikes'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  preferences: json['preferences'] as Map<String, dynamic>?,
  goals: (json['goals'] as List<dynamic>?)
      ?.map((e) => Goal.fromJson(e as Map<String, dynamic>))
      .toList(),
  personalityTraits: (json['personalityTraits'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  interactionHistory: InteractionHistory.fromJson(
    json['interactionHistory'] as Map<String, dynamic>,
  ),
);

Map<String, dynamic> _$UserProfileToJson(UserProfile instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'name': instance.name,
      'age': instance.age,
      'gender': instance.gender,
      'familyStatus': instance.familyStatus,
      'family': instance.family?.map((e) => e.toJson()).toList(),
      'location': instance.location?.toJson(),
      'facts': instance.facts?.map((e) => e.toJson()).toList(),
      'likes': instance.likes,
      'dislikes': instance.dislikes,
      'preferences': instance.preferences,
      'goals': instance.goals?.map((e) => e.toJson()).toList(),
      'personalityTraits': instance.personalityTraits,
      'interactionHistory': instance.interactionHistory.toJson(),
    };

InteractionHistory _$InteractionHistoryFromJson(Map<String, dynamic> json) =>
    InteractionHistory(
      lastUpdated: const TimestampDateTimeConverter().fromJson(
        json['lastUpdated'],
      ),
      sources: (json['sources'] as List<dynamic>?)
          ?.map((e) => InteractionSource.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$InteractionHistoryToJson(InteractionHistory instance) =>
    <String, dynamic>{
      'lastUpdated': const TimestampDateTimeConverter().toJson(
        instance.lastUpdated,
      ),
      'sources': instance.sources?.map((e) => e.toJson()).toList(),
    };
