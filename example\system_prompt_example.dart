// ignore_for_file: avoid_relative_lib_imports, avoid_print

import '../lib/utils/system_prompt_builder.dart';
import '../lib/models/models.dart';

/// Example demonstrating how the SystemPromptBuilder creates comprehensive
/// system prompts with UserProfile information.
void main() {
  // Create a sample user profile
  final userProfile = UserProfile(
    userId: 'user-123',
    name: '<PERSON>',
    age: 32,
    gender: 'female',
    familyStatus: 'married',
    location: Location(town: 'Austin', country: 'USA'),
    likes: ['yoga', 'reading', 'cooking', 'hiking'],
    dislikes: ['loud music', 'crowded spaces'],
    personalityTraits: ['empathetic', 'goal-oriented', 'creative'],
    goals: [
      Goal(
        id: 'goal-1',
        description: 'Start a small business',
        status: 'in_progress',
        createdAt: DateTime(2024, 1, 1),
      ),
      Goal(
        id: 'goal-2',
        description: 'Learn Spanish',
        status: 'not_started',
        createdAt: DateTime(2024, 1, 15),
      ),
    ],
    facts: [
      Fact(key: 'profession', value: 'Marketing Manager'),
      Fact(key: 'years_experience', value: '8'),
      Fact(key: 'education', value: 'MBA'),
    ],
    family: [
      RelationInfo(
        name: '<PERSON>',
        age: 34,
        relation: 'spouse',
        otherInfo: ['software engineer', 'supportive of career goals'],
      ),
      RelationInfo(
        name: 'Luna',
        age: 3,
        relation: 'dog',
        otherInfo: ['golden retriever', 'loves walks'],
      ),
    ],
    preferences: {
      'communication_style': 'encouraging and detailed',
      'session_frequency': 'weekly',
      'focus_areas': ['career development', 'work-life balance'],
    },
    interactionHistory: InteractionHistory(
      lastUpdated: DateTime(2024, 1, 20),
      sources: [],
    ),
  );

  // Generate system prompt
  final systemPrompt = SystemPromptBuilder.buildSystemPrompt(
    userProfile: userProfile,
    currentTime: DateTime(2024, 1, 22, 14, 30),
    chatObjective: 'Provide personalized career coaching and entrepreneurship guidance',
    additionalInstructions: [
      'Focus on actionable business development steps',
      'Consider work-life balance with family commitments',
      'Leverage her marketing background and MBA education',
      'Encourage progress tracking and milestone celebration',
    ],
  );

  print('=== GENERATED SYSTEM PROMPT ===\n');
  print(systemPrompt);
  print('\n=== END SYSTEM PROMPT ===');
}
