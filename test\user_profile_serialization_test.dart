import 'package:flutter_test/flutter_test.dart';
import 'package:upshift/models/models.dart';
import 'dart:convert';

void main() {
  group('UserProfile DateTime Serialization Tests', () {
    test('should serialize and deserialize UserProfile with DateTime objects correctly', () {
      // Create a test UserProfile with DateTime objects
      final testProfile = UserProfile(
        userId: 'test-user-123',
        name: 'Test User',
        age: 30,
        interactionHistory: InteractionHistory(
          lastUpdated: DateTime.parse('2024-01-15T10:30:00.000Z'),
          sources: [
            InteractionSource(
              sessionId: 'session-1',
              timestamp: DateTime.parse('2024-01-15T10:30:00.000Z'),
            ),
          ],
        ),
        goals: [
          Goal(
            id: 'goal-1',
            description: 'Test goal',
            status: 'in_progress',
            createdAt: DateTime.parse('2024-01-10T08:00:00.000Z'),
            updatedAt: DateTime.parse('2024-01-15T10:30:00.000Z'),
          ),
        ],
      );

      // Convert to JSON and back to verify serialization works
      final json = testProfile.toJson();
      final jsonString = jsonEncode(json);
      
      // Verify that the JSON can be parsed back
      final parsedJson = jsonDecode(jsonString) as Map<String, dynamic>;
      final reconstructedProfile = UserProfile.fromJson(parsedJson);
      
      // Verify basic fields
      expect(reconstructedProfile.userId, equals('test-user-123'));
      expect(reconstructedProfile.name, equals('Test User'));
      expect(reconstructedProfile.age, equals(30));
      
      // Verify DateTime fields are preserved
      expect(reconstructedProfile.interactionHistory.lastUpdated, isA<DateTime>());
      expect(reconstructedProfile.interactionHistory.sources?.length, equals(1));
      expect(reconstructedProfile.interactionHistory.sources?[0].timestamp, isA<DateTime>());
      
      expect(reconstructedProfile.goals?.length, equals(1));
      expect(reconstructedProfile.goals?[0].createdAt, isA<DateTime>());
      expect(reconstructedProfile.goals?[0].updatedAt, isA<DateTime>());
      
      // Verify the actual DateTime values are correct
      expect(
        reconstructedProfile.interactionHistory.lastUpdated.toUtc().toIso8601String(),
        equals('2024-01-15T10:30:00.000Z'),
      );
      expect(
        reconstructedProfile.interactionHistory.sources?[0].timestamp.toUtc().toIso8601String(),
        equals('2024-01-15T10:30:00.000Z'),
      );
      expect(
        reconstructedProfile.goals?[0].createdAt.toUtc().toIso8601String(),
        equals('2024-01-10T08:00:00.000Z'),
      );
      expect(
        reconstructedProfile.goals?[0].updatedAt?.toUtc().toIso8601String(),
        equals('2024-01-15T10:30:00.000Z'),
      );
    });

    test('should handle UserProfile with minimal required fields', () {
      // Create a minimal UserProfile
      final minimalProfile = UserProfile(
        userId: 'minimal-user',
        interactionHistory: InteractionHistory(
          lastUpdated: DateTime.now(),
        ),
      );

      // Convert to JSON and back
      final json = minimalProfile.toJson();
      final jsonString = jsonEncode(json);
      final parsedJson = jsonDecode(jsonString) as Map<String, dynamic>;
      final reconstructedProfile = UserProfile.fromJson(parsedJson);

      // Verify basic fields
      expect(reconstructedProfile.userId, equals('minimal-user'));
      expect(reconstructedProfile.name, isNull);
      expect(reconstructedProfile.age, isNull);
      expect(reconstructedProfile.goals, isNull);
      expect(reconstructedProfile.interactionHistory.lastUpdated, isA<DateTime>());
      expect(reconstructedProfile.interactionHistory.sources, isNull);
    });

    test('should handle null values in optional fields', () {
      final profileWithNulls = UserProfile(
        userId: 'test-user',
        name: null,
        age: null,
        gender: null,
        familyStatus: null,
        family: null,
        location: null,
        facts: null,
        likes: null,
        dislikes: null,
        preferences: null,
        goals: null,
        personalityTraits: null,
        interactionHistory: InteractionHistory(
          lastUpdated: DateTime.now(),
          sources: null,
        ),
      );

      // Convert to JSON and back
      final json = profileWithNulls.toJson();
      final jsonString = jsonEncode(json);
      final parsedJson = jsonDecode(jsonString) as Map<String, dynamic>;
      final reconstructedProfile = UserProfile.fromJson(parsedJson);

      // Verify all null fields remain null
      expect(reconstructedProfile.userId, equals('test-user'));
      expect(reconstructedProfile.name, isNull);
      expect(reconstructedProfile.age, isNull);
      expect(reconstructedProfile.gender, isNull);
      expect(reconstructedProfile.familyStatus, isNull);
      expect(reconstructedProfile.family, isNull);
      expect(reconstructedProfile.location, isNull);
      expect(reconstructedProfile.facts, isNull);
      expect(reconstructedProfile.likes, isNull);
      expect(reconstructedProfile.dislikes, isNull);
      expect(reconstructedProfile.preferences, isNull);
      expect(reconstructedProfile.goals, isNull);
      expect(reconstructedProfile.personalityTraits, isNull);
      expect(reconstructedProfile.interactionHistory.lastUpdated, isA<DateTime>());
      expect(reconstructedProfile.interactionHistory.sources, isNull);
    });
  });
}
