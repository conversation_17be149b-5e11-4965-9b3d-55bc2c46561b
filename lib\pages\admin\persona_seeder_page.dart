import 'package:flutter/material.dart';
import '../../utils/admin/system_persona_seeder.dart';
import '../../services/firestore.dart';

/// Admin page for seeding SystemPersona entities
/// This page provides a UI to seed the default personas into Firestore
/// Useful for development, testing, and initial setup
class PersonaSeederPage extends StatefulWidget {
  const PersonaSeederPage({super.key});

  @override
  State<PersonaSeederPage> createState() => _PersonaSeederPageState();
}

class _PersonaSeederPageState extends State<PersonaSeederPage> {
  bool _isLoading = false;
  bool _isCheckingExistence = true;
  bool _personasExist = false;
  int _personaCount = 0;
  String? _errorMessage;
  List<String>? _createdIds;

  @override
  void initState() {
    super.initState();
    _checkPersonaExistence();
  }

  Future<void> _checkPersonaExistence() async {
    setState(() {
      _isCheckingExistence = true;
      _errorMessage = null;
    });

    try {
      final count = await FirestoreService.getSystemPersonaCount();
      final exist = count > 0;
      
      setState(() {
        _personaCount = count;
        _personasExist = exist;
        _isCheckingExistence = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to check persona existence: $e';
        _isCheckingExistence = false;
      });
    }
  }

  Future<void> _seedPersonas() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _createdIds = null;
    });

    try {
      final ids = await SystemPersonaSeeder.seedDefaultPersonas();
      
      setState(() {
        _createdIds = ids;
        _isLoading = false;
      });
      
      // Refresh the existence check
      await _checkPersonaExistence();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Successfully created ${ids.length} personas!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to seed personas: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _seedIfEmpty() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _createdIds = null;
    });

    try {
      final ids = await SystemPersonaSeeder.seedIfEmpty();
      
      setState(() {
        _createdIds = ids;
        _isLoading = false;
      });
      
      // Refresh the existence check
      await _checkPersonaExistence();
      
      if (mounted) {
        if (ids.isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Personas already exist, no seeding needed.'),
              backgroundColor: Colors.orange,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Successfully created ${ids.length} personas!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to seed personas: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SystemPersona Seeder'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'SystemPersona Status',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    if (_isCheckingExistence)
                      const Row(
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 8),
                          Text('Checking existing personas...'),
                        ],
                      )
                    else ...[
                      Row(
                        children: [
                          Icon(
                            _personasExist ? Icons.check_circle : Icons.warning,
                            color: _personasExist ? Colors.green : Colors.orange,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            _personasExist 
                              ? 'Personas exist ($_personaCount found)'
                              : 'No personas found',
                            style: TextStyle(
                              color: _personasExist ? Colors.green : Colors.orange,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton.icon(
                        onPressed: _checkPersonaExistence,
                        icon: const Icon(Icons.refresh),
                        label: const Text('Refresh Status'),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Seeding Actions',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 16),
                    
                    ElevatedButton.icon(
                      onPressed: _isLoading ? null : _seedIfEmpty,
                      icon: _isLoading 
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.auto_fix_high),
                      label: const Text('Seed If Empty (Recommended)'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    ElevatedButton.icon(
                      onPressed: _isLoading ? null : _seedPersonas,
                      icon: _isLoading 
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.add_circle),
                      label: const Text('Force Seed (Creates Duplicates)'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    const Text(
                      'Note: "Seed If Empty" will only create personas if none exist. '
                      '"Force Seed" will always create new personas, potentially creating duplicates.',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            if (_errorMessage != null) ...[
              const SizedBox(height: 16),
              Card(
                color: Colors.red.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.error, color: Colors.red.shade700),
                          const SizedBox(width: 8),
                          Text(
                            'Error',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.red.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _errorMessage!,
                        style: TextStyle(color: Colors.red.shade700),
                      ),
                    ],
                  ),
                ),
              ),
            ],
            
            if (_createdIds != null && _createdIds!.isNotEmpty) ...[
              const SizedBox(height: 16),
              Card(
                color: Colors.green.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.check_circle, color: Colors.green.shade700),
                          const SizedBox(width: 8),
                          Text(
                            'Success',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.green.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Created ${_createdIds!.length} personas successfully!',
                        style: TextStyle(color: Colors.green.shade700),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
