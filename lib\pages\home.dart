import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

import 'gemini_chat.dart';
import '../services/firestore.dart';
import '../services/chat_title_generator.dart';
import '../models/models.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final _geminiApiKey = dotenv.env['GEMINI_API_KEY'] ?? '';
  late final ChatTitleGenerator _titleGenerator;

  List<Chat> _chats = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _titleGenerator = ChatTitleGenerator();
    _loadChats();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _loadChats() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser != null) {
        final chats = await FirestoreService.getChats(currentUser.uid);

        // Generate titles for chats that need them (in background)
        _generateTitlesForChatsIfNeeded(chats, currentUser.uid);

        setState(() {
          _chats = chats;
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = 'User not authenticated';
          _isLoading = false;
        });
      }
    } catch (e) {
      final errorMsg = 'Failed to load chats: $e';
      debugPrint('Error loading chats: $errorMsg');
      setState(() {
        _errorMessage = errorMsg;
        _isLoading = false;
      });
    }
  }

  /// Generates titles for chats that have empty titles or "New Chat" title
  Future<void> _generateTitlesForChatsIfNeeded(
    List<Chat> chats,
    String userId,
  ) async {
    for (final chat in chats) {
      if (chat.id != null && _shouldGenerateTitle(chat.title)) {
        // Run title generation in background without blocking UI
        _generateAndUpdateChatTitle(chat.id!, userId).catchError((error) {
          debugPrint('Error generating title for chat ${chat.id}: $error');
        });
      }
    }
  }

  /// Checks if a chat title needs to be generated
  bool _shouldGenerateTitle(String title) {
    return title.isEmpty || title == 'New Chat';
  }

  /// Generates and updates a chat title
  Future<void> _generateAndUpdateChatTitle(String chatId, String userId) async {
    try {
      // Get messages for the chat
      final messages = await FirestoreService.getMessages(chatId, userId);

      if (messages.isNotEmpty) {
        // Generate title using Gemini AI
        final newTitle = await _titleGenerator.generateTitle(messages);

        // Update the chat title in Firestore
        await FirestoreService.updateChatTitle(chatId, newTitle);

        // Update the local chat list if the chat is still visible
        if (mounted) {
          setState(() {
            final chatIndex = _chats.indexWhere((chat) => chat.id == chatId);
            if (chatIndex != -1) {
              _chats[chatIndex] = Chat(
                id: _chats[chatIndex].id,
                title: newTitle,
                ownerId: _chats[chatIndex].ownerId,
                startedDate: _chats[chatIndex].startedDate,
                lastUpdatedDate: _chats[chatIndex].lastUpdatedDate,
                isCompleted: _chats[chatIndex].isCompleted,
                participants: _chats[chatIndex].participants,
                archived: _chats[chatIndex].archived,
                metadata: _chats[chatIndex].metadata,
              );
            }
          });
        }
      }
    } catch (e) {
      debugPrint('Error generating title for chat $chatId: $e');
    }
  }

  Future<void> _createNewChat() async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('User not authenticated')));
        return;
      }

      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      // Create new chat in Firestore
      final chatId = await FirestoreService.createChat(currentUser.uid);

      // Hide loading indicator
      if (mounted) Navigator.of(context).pop();

      // Navigate to GeminiChat page
      if (mounted) {
        Navigator.of(context)
            .push(
              MaterialPageRoute(
                builder: (context) =>
                    GeminiChat(geminiApiKey: _geminiApiKey, chatId: chatId),
              ),
            )
            .then((_) {
              // Refresh the chat list when returning from the chat page
              _loadChats();
            });
      }
    } catch (e) {
      // Hide loading indicator if still showing
      if (mounted) Navigator.of(context).pop();

      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to create chat: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Your Chats'),
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadChats),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () => FirebaseAuth.instance.signOut(),
          ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: FloatingActionButton(
        onPressed: _createNewChat,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(onPressed: _loadChats, child: const Text('Retry')),
          ],
        ),
      );
    }

    if (_chats.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 64,
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No chats yet',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'Start a new conversation by tapping the + button',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _chats.length,
      itemBuilder: (context, index) {
        final chat = _chats[index];
        return _buildChatTile(chat);
      },
    );
  }

  Widget _buildChatTile(Chat chat) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Theme.of(context).colorScheme.primary,
          child: Icon(
            Icons.chat,
            color: Theme.of(context).colorScheme.onPrimary,
          ),
        ),
        title: Text(
          chat.title,
          style: Theme.of(context).textTheme.titleMedium,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Participants: ${chat.participants.length}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            const SizedBox(height: 2),
            Text(
              'Last updated: ${_formatDate(chat.lastUpdatedDate)}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (chat.isCompleted)
              Icon(
                Icons.check_circle,
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
            if (chat.archived == true)
              Icon(
                Icons.archive,
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.6),
                size: 20,
              ),
            const SizedBox(width: 8),
            const Icon(Icons.chevron_right),
          ],
        ),
        onTap: () {
          if (chat.id != null) {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) =>
                    GeminiChat(geminiApiKey: _geminiApiKey, chatId: chat.id!),
              ),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Chat ID not available')),
            );
          }
        },
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
